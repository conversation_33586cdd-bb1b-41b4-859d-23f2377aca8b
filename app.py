from flask import Flask, request, jsonify
import requests
from bs4 import BeautifulSoup
import json
import re

app = Flask(__name__)

# 配置API密钥 - 用户需要自行申请并填写
YOUTUBE_API_KEY = 'YOUR_YOUTUBE_API_KEY'
BILIBILI_API_KEY = 'YOUR_BILIBILI_API_KEY'  # 用户需要自行申请Bilibili API密钥

# 控制是否启用小红书搜索功能（默认启用，但可以设置为False以跳过）
ENABLE_XHS_SEARCH = True

@app.route('/search', methods=['GET'])
def search_videos():
    app.logger.info(f'收到搜索请求: {request.args.get("query")}')  # 添加请求日志
    query = request.args.get('query')
    if not query:
        return jsonify({'error': '请输入搜索关键词'}), 400
    
    results = []
    
    try:
        # 搜索YouTube视频
        if YOUTUBE_API_KEY and YOUTUBE_API_KEY != 'YOUR_YOUTUBE_API_KEY':
            try:
                youtube_url = f'https://www.googleapis.com/youtube/v3/search?part=snippet&q={query}&type=video&maxResults=10&key={YOUTUBE_API_KEY}'
                response = requests.get(youtube_url, timeout=10)  # 添加超时设置
                data = response.json()
                
                # 检查API错误响应
                if 'error' in data:
                    results.append({'error': f'YouTube API错误: {data["error"]["message"]}'})
                else:
                    for item in data.get('items', []):
                        thumbnail_url = item['snippet']['thumbnails']['default']['url']
                        # 确保图片URL是有效的
                        if not thumbnail_url.startswith(('http:', 'https:')):
                            thumbnail_url = 'https:' + thumbnail_url if thumbnail_url.startswith('//') else f'https://{thumbnail_url}'
                        
                        results.append({
                            'title': item['snippet']['title'],
                            'thumbnail': thumbnail_url,
                            'url': f'https://www.youtube.com/watch?v={item["id"]["videoId"]}',
                            'source': 'YouTube',
                            'duration': 'N/A'
                        })
            except Exception as e:
                results.append({'error': f'YouTube API调用错误: {str(e)}'})  # 返回错误信息
        else:
            results.append({'warning': 'YouTube搜索功能未启用，请配置有效的API密钥'})  # 提示API密钥未配置
        
        # 搜索Bilibili视频 (简化示例)
        try:
            bilibili_url = f'https://api.bilibili.com/x/web-interface/search/type?search_type=video&keyword={query}'
            # 添加更完整的浏览器请求头模拟，包括Cookie字段
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': 'https://www.bilibili.com/',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-site',
            }
            
            # 添加空的Cookie字段，即使没有真实的Cookie也可以帮助绕过一些基本的反爬虫机制
            cookies = {
                'CURRENT_FNVAL': '4048',
                'b_nut': '100',
                '_uuid': 'xxxxx',
            }
            
            response = requests.get(bilibili_url, headers=headers, cookies=cookies, timeout=10)
            # 检查响应状态码
            if response.status_code != 200:
                results.append({'error': f'Bilibili API请求被拒绝: 状态码 {response.status_code}', 'hint': '可能是请求头或频率限制问题'})
            else:
                try:
                    data = response.json()
                except Exception as e:
                    results.append({'error': f'Bilibili API返回非JSON格式数据: {str(e)}', 'content_preview': response.text[:200]})
                    return jsonify(results)
                
                # 检查响应数据
                if data.get('code', 0) != 0:
                    error_message = data.get("message", "未知错误")
                    results.append({'error': f'Bilibili API错误: {error_message} (code: {data.get("code")})'})
                else:
                    for item in data.get('data', {}).get('result', [])[:10]:  # 限制最多10个结果
                        # 处理标题中的HTML标签
                        title = item.get('title', '').replace('<em class="keyword">', '').replace('</em>', '')
                        # 确保封面URL是完整的
                        pic = item.get("pic", "")
                        if pic:
                            # 确保图片URL是有效的
                            if not pic.startswith(('http:', 'https:')):
                                pic = 'https:' + pic if pic.startswith('//') else f'https:{pic}'
                        else:
                            # 如果没有图片URL，使用默认图片
                            pic = 'https://via.placeholder.com/300x180?text=No+Image'
                        
                        results.append({
                            'title': title,
                            'thumbnail': pic,
                            'url': f'https://www.bilibili.com/video/{item["bvid"]}',
                            'source': 'Bilibili',
                            'duration': item.get('duration', 'N/A')
                        })
        except Exception as e:
            results.append({'error': f'Bilibili API调用错误: {str(e)}'})  # 返回错误信息

        # 搜索小红书笔记
        if ENABLE_XHS_SEARCH:
            try:
                # 首先尝试使用API方式
                xhs_url = f'https://www.xiaohongshu.com/api/search/v1?keyword={query}&page=1&page_size=10&sort=general'
                # 添加更完整的请求头，模拟真实浏览器请求
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Referer': 'https://www.xiaohongshu.com/search_result',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Connection': 'keep-alive',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-site',
                    'DNT': '1',
                    'Upgrade-Insecure-Requests': '1',
                }
                
                # 添加Cookie支持，即使没有真实的Cookie值也能帮助绕过基本的反爬虫机制
                cookies = {
                    'xhsuid': 'xxxxxxxx',
                    'customerClientId': 'xxxxxxxx',
                    'web_session': 'xxxxxxxx',
                    'gid': 'xxxxxxxx',
                }
                
                # 添加延迟以避免触发频率限制
                import time
                time.sleep(1)
                
                # 添加重试机制
                max_retries = 3
                retry_count = 0
                response = None
                
                while retry_count < max_retries:
                    try:
                        response = requests.get(xhs_url, headers=headers, cookies=cookies, timeout=20)
                        # 如果请求成功，跳出重试循环
                        if response.status_code != 503:
                            break
                    except requests.exceptions.RequestException as e:
                        # 如果是最后一次重试，抛出异常
                        if retry_count == max_retries - 1:
                            raise e
                        time.sleep(2 ** retry_count)  # 指数退避
                    else:
                        # 如果是503错误，进行重试
                        if response.status_code == 503:
                            retry_count += 1
                            if retry_count < max_retries:
                                time.sleep(2 ** retry_count)  # 指数退避
                        else:
                            # 非503错误直接跳出循环
                            break
                
                # 检查响应状态码
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        # 检查响应是否包含错误信息
                        if data.get('success') == False:
                            results.append({'error': f'小红书API返回错误: {data.get("msg", "未知错误")}'})
                        else:
                            for item in data.get('data', {}).get('items', [])[:10]:  # 限制最多10个结果
                                cover_url = item.get('note_info', {}).get('cover', {}).get('url') or item.get('note_info', {}).get('cover_url')
                                # 确保图片URL是有效的
                                if cover_url and not cover_url.startswith(('http:', 'https:')):
                                    cover_url = 'https:' + cover_url if cover_url.startswith('//') else f'https://{cover_url}'
                                elif not cover_url:
                                    # 如果没有图片URL，使用默认图片
                                    cover_url = 'https://via.placeholder.com/300x180?text=No+Image'
                                
                                results.append({
                            'title': item.get('note_info', {}).get('title', '无标题'),
                            'thumbnail': cover_url,
                            'url': f'https://www.xiaohongshu.com/discovery/item/{item.get("id", item.get("note_id"))}',
                            'source': '小红书',
                            'duration': 'N/A'
                        })
                    except Exception as e:
                        results.append({'error': f'小红书API返回数据格式错误: {str(e)}', 'content_preview': response.text[:200]})
                elif response.status_code == 403:
                    results.append({'error': '小红书API访问被拒绝，可能是反爬虫机制拦截', 'hint': '请稍后重试或检查请求参数'})
                elif response.status_code == 429:
                    results.append({'error': '小红书API请求频率超限', 'hint': '请降低请求频率或稍后重试'})
                elif response.status_code == 503:
                    # 当API返回503时，尝试使用网页抓取方式
                    results.append({'warning': '小红书API服务暂时不可用，正在尝试备用方案'})
                    try:
                        # 使用网页搜索方式作为备选方案
                        search_url = f'https://www.xiaohongshu.com/search_result?keyword={query}'
                        headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                            'Referer': 'https://www.xiaohongshu.com/',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        }
                        
                        response = requests.get(search_url, headers=headers, timeout=20)
                        if response.status_code == 200:
                            # 解析网页内容
                            soup = BeautifulSoup(response.text, 'html.parser')
                            
                            # 尝试从页面中提取JSON数据
                            script_tags = soup.find_all('script')
                            notes_found = False
                            
                            for script in script_tags:
                                if script.string and 'notes' in script.string:
                                    # 尝试提取JSON数据
                                    json_match = re.search(r'(\{.*"notes".*\})', script.string)
                                    if json_match:
                                        try:
                                            data = json.loads(json_match.group(1))
                                            notes = data.get('notes', [])
                                            for note in notes[:10]:  # 限制最多10个结果
                                                cover_url = note.get('cover', {}).get('url') or note.get('cover_url', '')
                                                if cover_url and not cover_url.startswith(('http:', 'https:')):
                                                    cover_url = 'https:' + cover_url if cover_url.startswith('//') else f'https://{cover_url}'
                                                elif not cover_url:
                                                    cover_url = 'https://via.placeholder.com/300x180?text=No+Image'
                                                    
                                                results.append({
                                                    'title': note.get('title', '无标题'),
                                                    'thumbnail': cover_url,
                                                    'url': f'https://www.xiaohongshu.com/discovery/item/{note.get("id")}',
                                                    'source': '小红书',
                                                    'duration': 'N/A'
                                                })
                                            notes_found = True
                                            break
                                        except json.JSONDecodeError:
                                            continue
                            
                            # 如果没有找到结构化数据，尝试解析DOM元素
                            if not notes_found:
                                note_elements = soup.find_all('div', {'class': 'note-item'})
                                if note_elements:
                                    for element in note_elements[:10]:  # 限制最多10个结果
                                        title_elem = element.find('div', {'class': 'title'})
                                        title = title_elem.text.strip() if title_elem else '无标题'
                                        
                                        img_elem = element.find('img')
                                        cover_url = img_elem.get('src', '') if img_elem else ''
                                        if cover_url and not cover_url.startswith(('http:', 'https:')):
                                            cover_url = 'https:' + cover_url if cover_url.startswith('//') else f'https://{cover_url}'
                                        elif not cover_url:
                                            cover_url = 'https://via.placeholder.com/300x180?text=No+Image'
                                            
                                        link_elem = element.find('a')
                                        url = f"https://www.xiaohongshu.com{link_elem.get('href')}" if link_elem and link_elem.get('href') else search_url
                                        
                                        results.append({
                                            'title': title,
                                            'thumbnail': cover_url,
                                            'url': url,
                                            'source': '小红书',
                                            'duration': 'N/A'
                                        })
                                else:
                                    # 如果找不到特定元素，添加一个通用提示
                                    results.append({'info': '小红书搜索完成，但页面结构可能已更改，请更新解析逻辑', 'url': search_url})
                        else:
                            results.append({'error': f'小红书网页搜索也失败了: 状态码 {response.status_code}'})
                    except Exception as e:
                        results.append({'error': f'小红书备用方案也失败了: {str(e)}'})
                else:
                    results.append({'error': f'小红书API请求失败: 状态码 {response.status_code}', 'hint': '可能是认证或频率限制问题'})
            except requests.exceptions.Timeout:
                results.append({'error': '小红书API请求超时，请稍后重试'})
            except requests.exceptions.ConnectionError as e:
                results.append({'error': f'小红书API网络连接错误: {str(e)}', 'hint': '请检查网络连接或稍后重试'})
            except requests.exceptions.RequestException as e:
                results.append({'error': f'小红书API请求异常: {str(e)}'})
            except Exception as e:
                results.append({'error': f'小红书API调用错误: {str(e)}'})
        else:
            results.append({'warning': '小红书搜索功能未启用'})

        # 搜索Coursera课程
        try:
            coursera_url = f'https://api.coursera.org/api/courses.v1?q={query}&limit=10'
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            }
            response = requests.get(coursera_url, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                for item in data.get('elements', [])[:10]:
                    results.append({
                        'title': item.get('name', '无标题'),
                        'thumbnail': item.get('photoUrl', 'https://via.placeholder.com/300x180?text=No+Image'),
                        'url': f'https://www.coursera.org/learn/{item.get("slug", "")}',
                        'source': 'Coursera',
                        'duration': 'N/A'
                    })
            else:
                results.append({'error': f'Coursera API请求失败: 状态码 {response.status_code}'})
        except Exception as e:
            results.append({'error': f'Coursera API调用错误: {str(e)}'})

        # 搜索edX课程
        try:
            edx_url = f'https://www.edx.org/api/v1/courses/search?search_query={query}&page_size=10'
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            }
            response = requests.get(edx_url, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                for item in data.get('results', [])[:10]:
                    results.append({
                        'title': item.get('title', '无标题'),
                        'thumbnail': item.get('image_url', 'https://via.placeholder.com/300x180?text=No+Image'),
                        'url': f'https://www.edx.org{item.get("url", "")}',
                        'source': 'edX',
                        'duration': 'N/A'
                    })
            else:
                results.append({'error': f'edX API请求失败: 状态码 {response.status_code}'})
        except Exception as e:
            results.append({'error': f'edX API调用错误: {str(e)}'})

        # 搜索网易云课堂课程
        try:
            netease_url = f'https://study.163.com/p/search/studycourse.json?keyword={query}&pageIndex=1&pageSize=10'
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': 'https://study.163.com/',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            }
            # 添加cookies提高请求成功率
            cookies = {
                'NTESSTUDYSI': 'xxxxxxxx',
                'EDUCLOUD': 'xxxxxxxx',
            }
            
            response = requests.get(netease_url, headers=headers, cookies=cookies, timeout=15)
            app.logger.info(f'网易云课堂API响应状态码: {response.status_code}')
            
            # 检查响应状态码
            if response.status_code == 200:
                # 检查响应内容是否为空
                if not response.text:
                    results.append({'error': '网易云课堂API返回空响应'})
                else:
                    # 记录响应内容长度用于调试
                    app.logger.info(f'网易云课堂API响应内容长度: {len(response.text)}')
                    try:
                        data = response.json()
                        # 检查返回的数据结构
                        if not data:
                            results.append({'error': '网易云课堂API返回空JSON对象'})
                        else:
                            for item in data.get('result', {}).get('list', [])[:10]:
                                results.append({
                                    'title': item.get('productName', '无标题'),
                                    'thumbnail': item.get('coverUrl', 'https://via.placeholder.com/300x180?text=No+Image'),
                                    'url': f'https://study.163.com/course/courseMain.htm?courseId={item.get("productId", "")}',
                                    'source': '网易云课堂',
                                    'duration': f'{item.get("lessonCount", 0)}课时'
                                })
                    except json.JSONDecodeError as e:
                        results.append({'error': f'网易云课堂API返回非JSON格式数据: {str(e)}', 'content_preview': response.text[:200]})
            elif response.status_code == 403:
                # 当API访问被拒绝时，尝试使用网页抓取方式
                results.append({'warning': '网易云课堂API访问被拒绝，正在尝试备用网页抓取方案'})
                try:
                    # 使用网页搜索方式作为备选方案
                    search_url = f'https://study.163.com/search?keyword={query}'
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Referer': 'https://study.163.com/',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    }
                    
                    response = requests.get(search_url, headers=headers, timeout=20)
                    if response.status_code == 200:
                        # 解析网页内容
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # 尝试从页面中提取课程数据
                        course_items = soup.find_all('div', {'class': 'course-card'})[:10]  # 限制最多10个结果
                        if course_items:
                            for item in course_items:
                                # 提取标题
                                title_elem = item.find('h3') or item.find('a')
                                title = title_elem.text.strip() if title_elem else '无标题'
                                
                                # 提取图片
                                img_elem = item.find('img')
                                cover_url = img_elem.get('src', '') if img_elem else ''
                                if cover_url and not cover_url.startswith(('http:', 'https:')):
                                    cover_url = 'https:' + cover_url if cover_url.startswith('//') else f'https:{cover_url}'
                                elif not cover_url:
                                    cover_url = 'https://via.placeholder.com/300x180?text=No+Image'
                                
                                # 提取链接
                                link_elem = item.find('a')
                                url = f"https://study.163.com{link_elem.get('href')}" if link_elem and link_elem.get('href') else search_url
                                
                                # 提取课时信息（如果有的话）
                                lesson_elem = item.find('span', class_='lesson-count')
                                duration = lesson_elem.text.strip() if lesson_elem else 'N/A'
                                
                                results.append({
                                    'title': title,
                                    'thumbnail': cover_url,
                                    'url': url,
                                    'source': '网易云课堂',
                                    'duration': duration
                                })
                        else:
                            # 如果找不到特定元素，尝试另一种选择器
                            course_items = soup.find_all('a', {'class': 'course-link'})[:10]
                            for item in course_items:
                                title = item.get('title', '无标题')
                                url = f"https://study.163.com{item.get('href')}" if item.get('href') else search_url
                                img_elem = item.find('img')
                                cover_url = img_elem.get('src', '') if img_elem else 'https://via.placeholder.com/300x180?text=No+Image'
                                
                                if cover_url and not cover_url.startswith(('http:', 'https:')):
                                    cover_url = 'https:' + cover_url if cover_url.startswith('//') else f'https:{cover_url}'
                                    
                                results.append({
                                    'title': title,
                                    'thumbnail': cover_url,
                                    'url': url,
                                    'source': '网易云课堂',
                                    'duration': 'N/A'
                                })
                            
                            # 如果还是找不到，添加提示信息
                            if not course_items:
                                results.append({'info': '网易云课堂搜索完成，但未找到匹配的课程元素，请更新解析逻辑', 'url': search_url})
                    else:
                        results.append({'error': f'网易云课堂网页搜索也失败了: 状态码 {response.status_code}'})
                except Exception as e:
                    results.append({'error': f'网易云课堂备用方案也失败了: {str(e)}'})
            else:
                results.append({'error': f'网易云课堂API请求失败: 状态码 {response.status_code}'})
        except requests.exceptions.Timeout:
            results.append({'error': '网易云课堂API请求超时，请稍后重试'})
        except requests.exceptions.RequestException as e:
            results.append({'error': f'网易云课堂API网络请求错误: {str(e)}'})
        except Exception as e:
            results.append({'error': f'网易云课堂API调用错误: {str(e)}'})

    except Exception as e:
        app.logger.error(f'搜索处理错误: {str(e)}')
        return jsonify({'error': '搜索处理错误: ' + str(e)}), 500
    return jsonify(results)

@app.route('/')
def index():
    return app.send_static_file('index.html')

if __name__ == '__main__':
    app.run(debug=True, port=5000)
from flask import Flask, request, jsonify
import requests

app = Flask(__name__)

# 配置API密钥 - 用户需要自行申请并填写
YOUTUBE_API_KEY = 'YOUR_YOUTUBE_API_KEY'
BILIBILI_API_KEY = 'YOUR_BILIBILI_API_KEY'

@app.route('/search', methods=['GET'])
def search_videos():
    app.logger.info(f'收到搜索请求: {request.args.get("query")}')  # 添加请求日志
    query = request.args.get('query')
    if not query:
        return jsonify({'error': '请输入搜索关键词'}), 400
    
    results = []
    
    # 搜索YouTube视频
    if YOUTUBE_API_KEY and YOUTUBE_API_KEY != 'YOUR_YOUTUBE_API_KEY':
        try:
            youtube_url = f'https://www.googleapis.com/youtube/v3/search?part=snippet&q={query}&type=video&maxResults=10&key={YOUTUBE_API_KEY}'
            response = requests.get(youtube_url, timeout=10)  # 添加超时设置
            data = response.json()
            
            # 检查API错误响应
            if 'error' in data:
                results.append({'error': f'YouTube API错误: {data["error"]["message"]}'})
            else:
                for item in data.get('items', []):
                    results.append({
                        'title': item['snippet']['title'],
                        'thumbnail': item['snippet']['thumbnails']['default']['url'],
                        'url': f'https://www.youtube.com/watch?v={item["id"]["videoId"]}',
                        'source': 'YouTube',
                        'duration': 'N/A'
                    })
        except Exception as e:
            results.append({'error': f'YouTube API调用错误: {str(e)}'})  # 返回错误信息
    else:
        results.append({'warning': 'YouTube搜索功能未启用，请配置有效的API密钥'})  # 提示API密钥未配置
    
    # 搜索Bilibili视频 (简化示例)
    try:
        bilibili_url = f'https://api.bilibili.com/x/web-interface/search/type?keyword={query}&search_type=video&page=1&pagesize=10'
        response = requests.get(bilibili_url, timeout=10)  # 添加超时设置
        data = response.json()
        
        for item in data.get('data', {}).get('result', []):
            results.append({
                'title': item['title'],
                'thumbnail': item['pic'],
                'url': f'https://www.bilibili.com/video/{item["bvid"]}',
                'source': 'Bilibili',
                'duration': item['duration']
            })
    except Exception as e:
        results.append({'error': f'Bilibili API调用错误: {str(e)}'})  # 返回错误信息
    
    except Exception as e:
        app.logger.error(f'搜索处理错误: {str(e)}')
        return jsonify({'error': '搜索处理错误: ' + str(e)}), 500
    return jsonify(results)

@app.route('/')
def index():
    return app.send_static_file('index.html')

if __name__ == '__main__':
    app.run(debug=True, port=5000)
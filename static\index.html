<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生学习视频搜索系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }
        .search-container {
            display: flex;
            max-width: 700px;
            margin: 0 auto 30px;
            gap: 10px;
        }
        #search-input {
            flex: 1;
            padding: 14px 20px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        #search-input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.3);
        }
        #search-btn {
            padding: 14px 24px;
            background-color: #4361ee;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(67, 97, 238, 0.3);
        }
        #search-btn:hover {
            background-color: #3a0ca3;
            transform: translateY(-2px);
        }
        #results-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .video-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.12);
        }
        .video-thumbnail {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-bottom: 1px solid #eee;
        }
        .video-info {
            padding: 16px;
        }
        .video-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2d3748;
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .video-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #718096;
        }
        .source-tag {
            background-color: #edf2f7;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2rem;
            color: #4a5568;
            display: none;
        }
        .error-message {
            text-align: center;
            padding: 20px;
            background-color: #fff5f5;
            color: #e53e3e;
            border-radius: 8px;
            display: none;
        }
        .no-results {
            text-align: center;
            padding: 40px;
            font-size: 1.2rem;
            color: #4a5568;
            display: none;
        }
        
        /* 视频播放器模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
        }
        
        .modal-content {
            position: relative;
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border: none;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .close {
            color: #aaa;
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 35px;
            font-weight: bold;
            z-index: 1001;
            cursor: pointer;
        }
        
        .close:hover,
        .close:focus {
            color: black;
        }
        
        .video-player-container {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 宽高比 */
            height: 0;
            overflow: hidden;
            border-radius: 10px 10px 0 0;
        }
        
        .video-player-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .video-details {
            padding: 20px;
        }
        
        .video-details h2 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .video-details p {
            color: #666;
            margin-bottom: 15px;
        }
        
        .video-source {
            display: inline-block;
            background-color: #4361ee;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .search-container {
                flex-direction: column;
            }
            #results-container {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
            .modal-content {
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>学生学习视频搜索系统</h1>
            <p>输入关键词，全网搜索相关学习视频</p>
        </header>

        <div class="search-container">
            <input type="text" id="search-input" placeholder="请输入搜索关键词，例如：Python基础教程">
            <button id="search-btn">搜索视频</button>
        </div>

        <div class="loading">搜索中，请稍候...</div>
        <div class="error-message">搜索出错，请重试</div>
        <div class="no-results">未找到相关视频，请尝试其他关键词</div>

        <div id="results-container"></div>
    </div>
    
    <!-- 视频播放模态框 -->
    <div id="videoModal" class="modal">
        <span class="close">&times;</span>
        <div class="modal-content">
            <div class="video-player-container">
                <iframe id="videoPlayer" src="" allowfullscreen></iframe>
            </div>
            <div class="video-details">
                <h2 id="videoTitle"></h2>
                <p id="videoSource"></p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const searchInput = document.getElementById('search-input');
            const searchBtn = document.getElementById('search-btn');
            const resultsContainer = document.getElementById('results-container');
            const loading = document.querySelector('.loading');
            const errorMessage = document.querySelector('.error-message');
            const noResults = document.querySelector('.no-results');
            
            // 视频播放模态框相关元素
            const modal = document.getElementById('videoModal');
            const videoPlayer = document.getElementById('videoPlayer');
            const videoTitle = document.getElementById('videoTitle');
            const videoSource = document.getElementById('videoSource');
            const closeBtn = document.querySelector('.close');

            // 默认图片URL，当图片加载失败时使用
            const defaultImage = 'https://via.placeholder.com/300x180?text=Image+Not+Available';

            // 处理图片加载失败的情况
            const handleImageError = (imgElement) => {
                if (imgElement.src !== defaultImage) {
                    imgElement.src = defaultImage;
                }
            };

            // 关闭模态框
            closeBtn.onclick = function() {
                modal.style.display = 'none';
                videoPlayer.src = '';
            }

            // 点击模态框外部区域关闭
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = 'none';
                    videoPlayer.src = '';
                }
            }

            // 根据不同平台生成内嵌播放器URL
            const getEmbedUrl = (videoUrl, source) => {
                if (source === 'YouTube') {
                    // 从YouTube URL中提取视频ID
                    const videoId = videoUrl.split('v=')[1];
                    const ampersandPosition = videoId.indexOf('&');
                    if (ampersandPosition !== -1) {
                        return `https://www.youtube.com/embed/${videoId.substring(0, ampersandPosition)}`;
                    }
                    return `https://www.youtube.com/embed/${videoId}`;
                } else if (source === 'Bilibili') {
                    // 从Bilibili URL中提取视频ID
                    const bvid = videoUrl.split('/video/')[1];
                    return `https://player.bilibili.com/player.html?bvid=${bvid}&page=1&as_wide=1&high_quality=1&danmaku=0`;
                } else {
                    // 对于其他平台，返回原URL（会在新标签页打开）
                    return videoUrl;
                }
            };

            // 打开视频播放器
            const openVideoPlayer = (video) => {
                // 对于支持内嵌播放的平台（YouTube和Bilibili）
                if (video.source === 'YouTube' || video.source === 'Bilibili') {
                    videoPlayer.src = getEmbedUrl(video.url, video.source);
                    videoTitle.textContent = video.title;
                    videoSource.textContent = `来源: ${video.source}`;
                    videoSource.className = 'video-source';
                    modal.style.display = 'block';
                } else {
                    // 对于不支持内嵌播放的平台，仍然在新标签页打开
                    window.open(video.url, '_blank');
                }
            };

            // 处理搜索请求
            const handleSearch = async () => {
                const query = searchInput.value.trim();
                if (!query) {
                    alert('请输入搜索关键词');
                    return;
                }

                // 显示加载状态，隐藏其他状态
                loading.style.display = 'block';
                errorMessage.style.display = 'none';
                noResults.style.display = 'none';
                resultsContainer.innerHTML = '';

                try {
                    const response = await fetch(`/search?query=${encodeURIComponent(query)}`);
                    if (!response.ok) throw new Error('搜索失败');

                    const videos = await response.json();
                    loading.style.display = 'none';

                    // 检查是否有错误或警告信息
                    const hasErrors = videos.some(video => video.error);
                    const hasWarnings = videos.some(video => video.warning);
                    const hasVideos = videos.some(video => video.title && !video.error && !video.warning);

                    if (!hasVideos && !hasErrors && !hasWarnings) {
                        noResults.style.display = 'block';
                        return;
                    }

                    // 渲染视频列表
                    videos.forEach(video => {
                        const card = document.createElement('div');
                        card.className = 'video-card';
                        card.innerHTML = `
                            <img src="${video.thumbnail || defaultImage}" alt="${video.title}" class="video-thumbnail" onerror="handleImageError(this)">
                            <div class="video-info">
                                <h3 class="video-title">${video.title || video.error || video.warning}</h3>
                                <div class="video-meta">
                                    <span class="source-tag">${video.source || '系统消息'}</span>
                                    <span>${video.duration || ''}</span>
                                </div>
                            </div>
                        `;

                        // 点击卡片播放视频
                        card.addEventListener('click', () => {
                            openVideoPlayer(video);
                        });

                        resultsContainer.appendChild(card);
                    });
                } catch (error) {
                    console.error('搜索错误:', error);
                    loading.style.display = 'none';
                    errorMessage.style.display = 'block';
                }
            };

            // 绑定搜索按钮点击事件
            searchBtn.addEventListener('click', handleSearch);

            // 绑定回车键事件
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') handleSearch();
            });
            
            // 将图片错误处理函数暴露到全局作用域
            window.handleImageError = handleImageError;
        });
    </script>
</body>
</html>
# 学生学习视频搜索系统

一个基于Flask的Web应用，能够根据用户输入的关键词全网搜索学习视频资源，目前支持YouTube和Bilibili平台。

## 功能特点
- 简洁美观的用户界面，支持关键词搜索
- 整合多个视频平台资源，提供丰富学习内容
- 视频结果以卡片式展示，包含标题、缩略图、来源和时长
- 响应式设计，适配不同设备屏幕

## 安装指南

### 前提条件
- Python 3.8+ 环境
- 网络连接
- （可选）YouTube API密钥（如需使用YouTube搜索功能）

### 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd student-learning-system
   ```

2. **创建并激活虚拟环境（推荐）**
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate
   
   # macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **安装依赖包**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置API密钥**
   编辑`app.py`文件，替换以下占位符：
   ```python
   YOUTUBE_API_KEY = 'YOUR_YOUTUBE_API_KEY'  # 替换为你的YouTube API密钥
   BILIBILI_API_KEY = 'YOUR_BILIBILI_API_KEY'  # Bilibili API目前无需密钥，可留空
   ```

   > 获取YouTube API密钥：
   > 1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
   > 2. 创建新项目并启用YouTube Data API v3
   > 3. 创建API密钥并复制到上述位置

## 使用方法

1. **启动应用**
   ```bash
   python app.py
   ```

2. **访问系统**
   打开浏览器访问：`http://localhost:5000`

3. **搜索视频**
   - 在搜索框中输入关键词（例如："Python基础教程"）
   - 点击"搜索视频"按钮
   - 浏览搜索结果，点击视频卡片打开对应视频

## 项目结构
```
student-learning-system/
├── app.py               # Flask应用主程序
├── requirements.txt     # 项目依赖
├── README.md            # 项目说明文档
└── static/
    └── index.html       # 前端页面
```

## 技术栈
- **后端**：Python, Flask
- **前端**：HTML, CSS, JavaScript
- **API集成**：YouTube Data API, Bilibili API
- **HTTP客户端**：Requests

## 注意事项
- 本项目仅用于学习目的，请勿用于商业用途
- 视频内容版权归各平台和上传者所有
- YouTube搜索功能需要有效的API密钥才能使用
- 部分平台API可能有调用频率限制

## 未来改进
- 添加更多视频平台支持（Coursera, edX, 网易云课堂等）
- 实现视频分类和筛选功能
- 添加用户收藏和历史记录功能
- 优化搜索算法，提高结果相关性
- 添加视频播放时长和评分显示